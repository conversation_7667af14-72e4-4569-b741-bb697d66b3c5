import SwiftUI

// MARK: - 简化的魔法光环效果
struct MagicAura: View {
    @State private var scale: CGFloat = 0.9
    @State private var opacity: Double = 0.5

    var body: some View {
        let gradient = LinearGradient(
            colors: [.animalSuccess, .animalAccent],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )

        return Circle()
            .stroke(gradient, lineWidth: 3)
            .frame(width: 130, height: 130)
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    scale = 1.1
                    opacity = 0.3
                }
            }
    }
}

// MARK: - 宠物出现动画
struct PetAppearanceAnimation: ViewModifier {
    @State private var scale: CGFloat = 0.1
    @State private var rotation: Double = -180
    @State private var opacity: Double = 0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .rotationEffect(.degrees(rotation))
            .opacity(opacity)
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.6, blendDuration: 0.3)) {
                    scale = 1.0
                    rotation = 0
                    opacity = 1.0
                }
            }
    }
}

struct WordTranlateView: View {
    let viewModel: RandomWordViewModel
    var body: some View {
        if let wordItem = viewModel.currentWordItem {
            
            // 音标和词根信息面板 - 游戏未完成时显示
            if viewModel.gameState != .correct {
                VStack(spacing: 8) {
                    // 音标显示区域（在顶部）
                    HStack {
                        Spacer()
                        HStack(spacing: 4) {
                            
                            
                            // 播放按钮
                            Button(action: {
                                SpeechService.shared.speak(wordItem.word)
                            }) {
                                Text(wordItem.phonetic)
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .fontDesign(.rounded)
                                    .foregroundColor(Color.animalAccent)
                                Image(systemName: "play.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(Color.animalAccent)
                            }
                            
                            // 组状态标识（仅在路径学习模式下显示）
                            if let groupStatus = viewModel.getCurrentWordGroupStatus() {
                                Text("·\(groupStatus)")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .fontDesign(.rounded)
                                    .foregroundColor(Color.animalSecondaryText)
                            }
                        }
                        Spacer()
                    }
                    
                    // 词根信息显示（如果有词根）
                    if viewModel.getTargetComponents().count > 0 {
                        let morphemes = viewModel.getTargetComponents().compactMap { component -> (String, MorphemeType)? in
                            if case .morpheme(let text, let type) = component {
                                return (text, type)
                            }
                            return nil
                        }
                        
                        if !morphemes.isEmpty {
                            // 词根说明横向滚动
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(morphemes, id: \.0) { text, type in
                                        HStack(spacing: 4) {
                                            Text(text)
                                                .font(.caption)
                                                .fontWeight(.bold)
                                                .fontDesign(.rounded)
                                                .padding(.horizontal, 6)
                                                .padding(.vertical, 3)
                                                .background(backgroundForMorphemeType(type))
                                                .foregroundColor(.white)
                                                .clipShape(RoundedRectangle(cornerRadius: 4))
                                            
                                            Text(getMorphemeDescription(text, type))
                                                .font(.caption2)
                                                .fontDesign(.rounded)
                                                .foregroundColor(Color.animalSecondaryText)
                                        }
                                    }
                                }
                                .padding(.horizontal, 4)
                            }
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.animalCardBackground.opacity(0.9), in: .rect(cornerRadius: 10))
                .shadow(color: .animalBorder.opacity(0.2), radius: 4, x: 0, y: 2)
            }
            
            // 中文含义显示
            VStack(spacing: 12) {
                
                // 中文含义显示（如果已拥有该宠物则显示特殊颜色）
                Text(wordItem.meaning)
                    .font(.title2)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalTextColor)
                    .padding()
                    .background(.regularMaterial, in: .rect(cornerRadius: 16))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            }
        }
    }
    
    private func backgroundForMorphemeType(_ type: MorphemeType) -> some ShapeStyle {
        switch type {
        case .prefix: return AnyShapeStyle(Color.animalPrefix)
        case .root: return AnyShapeStyle(Color.animalRoot)
        case .suffix: return AnyShapeStyle(Color.animalSuffix)
        }
    }
    
    private func getMorphemeDescription(_ text: String, _ type: MorphemeType) -> String {
        switch type {
        case .prefix:
            return WordMorphologyService.commonPrefixes[text] ?? "前缀"
        case .root:
            return WordMorphologyService.commonRoots[text] ?? "词根"
        case .suffix:
            return WordMorphologyService.commonSuffixes[text] ?? "后缀"
        }
    }
}

struct WordDisplayView: View {
    let viewModel: RandomWordViewModel
    @State private var currentWord: String = ""
    @State private var flipAngle: Double = 0

    var body: some View {
        if let wordItem = viewModel.currentWordItem {
            VStack(spacing: 20) {

                // 宠物显示区域
                if viewModel.showPet {
                    ZStack {
                        // 魔法光环背景
                        MagicAura()
                            .allowsHitTesting(false)

                        VStack(spacing: 12) {
                            // 宠物图片
                            Image(viewModel.petManager.getPetImageName(for: wordItem.word))
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 100, height: 100)
                                .background(.ultraThinMaterial, in: .circle)
                                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                                .modifier(PetAppearanceAnimation())

                            // 宠物信息提示
                            Group {
                                if viewModel.isWordOwned {
                                    // 已拥有宠物，显示经验值
                                    VStack(spacing: 4) {
                                        Text("我的宠物")
                                            .font(.caption)
                                            .fontWeight(.bold)
                                            .fontDesign(.rounded)
                                            .foregroundColor(Color.animalSecondaryText)

                                        HStack(spacing: 4) {
                                            Image(systemName: "star.fill")
                                                .foregroundColor(Color.animalSecondaryText)
                                                .font(.caption)

                                            Text("经验值: \(viewModel.getCurrentWordExp())")
                                                .font(.caption)
                                                .fontWeight(.bold)
                                                .fontDesign(.rounded)
                                                .foregroundColor(Color.animalSecondaryText)
                                        }
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.animalCardBackground.opacity(0.85), in: .rect(cornerRadius: 8))
                                } else {
                                    // 未拥有宠物，显示获得提示
                                    VStack(spacing: 4) {
                                        Text("野生宠物出现！")
                                            .font(.caption)
                                            .fontWeight(.bold)
                                            .fontDesign(.rounded)
                                            .foregroundColor(Color.animalSuccess)

                                        Text("拼写正确就有机会抓住")
                                            .font(.caption2)
                                            .fontDesign(.rounded)
                                            .foregroundColor(Color.animalSuccess.opacity(0.8))
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.animalCardBackground.opacity(0.85), in: .rect(cornerRadius: 8))
                                }
                            }
                        }
                    }
                } else {
                    // 单词图片
                    WordImageView(word: wordItem.word, wordDetailManager: viewModel.wordDetailManager)
                }


            }
            .onAppear {
                currentWord = wordItem.word
            }
        }
    }


}


import SwiftUI

// MARK: - 成功脉冲效果
struct SuccessPulse: ViewModifier {
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeOut(duration: 0.6)) {
                    scale = 1.2
                    opacity = 0.3
                }
            }
    }
}

struct GameStateView: View {
    let viewModel: RandomWordViewModel
    @Binding var showCategorySelection: Bool
    
    @State private var showCelebration = false

    var body: some View {
        Group {
            if viewModel.gameState == .correct {
                correctStateView
            } else if viewModel.gameState == .outOfEnergy {
                outOfEnergyStateView
            } else if viewModel.currentWordItem == nil {
                noWordStateView
            }
        }
    }

    private var correctStateView: some View {
        ZStack {
            
            VStack(spacing: 16) {
                HStack(spacing: 16) {
                    HStack {
                        Image(systemName: viewModel.hasErrorInCurrentWord ? "x.circle.fill" : "checkmark.circle.fill")
                            .foregroundColor(viewModel.hasErrorInCurrentWord ? Color.animalError : Color.animalSuccess)
                            .font(.title)
                            .modifier(viewModel.hasErrorInCurrentWord ? AnyViewModifier(EmptyModifier()) : AnyViewModifier(SuccessPulse()))

                        Text(viewModel.hasErrorInCurrentWord ? "错误" : "正确！")
                            .font(.title2)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(viewModel.hasErrorInCurrentWord ? Color.animalError : Color.animalSuccess)
                    }
                    
                    // 标星按钮
                    if let wordItem = viewModel.currentWordItem {
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.4)) {
                                viewModel.toggleCurrentWordStar()
                            }
                        }) {
                            Image(systemName: wordItem.isStar ? "star.fill" : "star")
                                .font(.title2)
                                .foregroundColor(wordItem.isStar ? Color.animalSecondaryText : Color.animalSecondaryText.opacity(0.5))
                                .background(
                                    Circle()
                                        .fill(.regularMaterial)
                                        .frame(width: 40, height: 40)
                                )
                                .scaleEffect(wordItem.isStar ? 1.1 : 1.0)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        }
                        .buttonStyle(.plain)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: wordItem.isStar)
                    }
                }

                // 错词本相关提示（错误时或从错词本中移除时）
                if viewModel.showError && !viewModel.errorMessage.isEmpty {
                    Text(viewModel.errorMessage)
                        .font(.body)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .foregroundColor(viewModel.hasErrorInCurrentWord ? Color.animalError : Color.animalSuccess)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background((viewModel.hasErrorInCurrentWord ? Color.animalError : Color.animalSuccess).opacity(0.1), in: .rect(cornerRadius: 8))
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity),
                            removal: .opacity.animation(.easeInOut(duration: 0.3))
                        ))
                }

                // 奖励显示区域
                if viewModel.showPet || viewModel.gainedExp {
                    VStack(spacing: 8) {
                        // 宠物奖励
                        if viewModel.showPet {
                            HStack {
                                Image(systemName: viewModel.gotNewPet ? "pawprint.circle.fill" : "pawprint.fill")
                                    .foregroundColor(Color.animalPrefix)
                                    .font(.title2)
                                    .modifier(viewModel.gotNewPet ? AnyViewModifier(SuccessPulse()) : AnyViewModifier(EmptyModifier()))

                                Text(viewModel.gotNewPet ? "获得新宠物！" : "可惜，未获得")
                                    .font(.body)
                                    .fontWeight(.bold)
                                    .fontDesign(.rounded)
                                    .foregroundColor(Color.animalPrefix)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.animalPrefix.opacity(0.1), in: .rect(cornerRadius: 12))
                            .transition(.scale.combined(with: .opacity))
                        }

                        // 经验值奖励
                        if viewModel.gainedExp {
                            HStack {
                                Image(systemName: "star.fill")
                                    .foregroundColor(Color.animalSecondaryText)
                                    .font(.title2)
                                    .modifier(SuccessPulse())

                                Text("经验值 +1")
                                    .font(.body)
                                    .fontWeight(.bold)
                                    .fontDesign(.rounded)
                                    .foregroundColor(Color.animalSecondaryText)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.animalSecondaryText.opacity(0.1), in: .rect(cornerRadius: 12))
                        }
                    }
                }

                Button("下一个单词") {
                    showCelebration = false
                    viewModel.nextWord()
                }
                .font(.headline)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.animalButton.gradient, in: .capsule)
                .shadow(color: Color.animalButton.opacity(0.3), radius: 8, x: 0, y: 4)

            }
            .padding()
            .background(Color.animalCardBackground.opacity(0.8), in: .rect(cornerRadius: 16))
        }
        .onAppear {
            // 如果是正确且无错误，显示庆祝动画
            if !viewModel.hasErrorInCurrentWord {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    showCelebration = true
                }
            }
        }
        .onChange(of: viewModel.gameState) { oldValue, newValue in
            if newValue == .correct && !viewModel.hasErrorInCurrentWord {
                showCelebration = true
            }
        }
    }

    private var outOfEnergyStateView: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "heart.slash.fill")
                    .foregroundColor(Color.animalError)
                    .font(.title)

                Text("体力值不足！")
                    .font(.title2)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalError)
            }

            Text("体力值将在 \(viewModel.getTimeUntilReset()) 后重置")
                .font(.body)
                .fontDesign(.rounded)
                .foregroundStyle(Color.animalSecondaryText)
                .multilineTextAlignment(.center)

            Button("明天再来") {
                if viewModel.retryAfterEnergyCheck() {
                    // 成功重试，继续游戏
                } else {
                    // 仍然没有体力，显示提示
                }
            }
            .font(.headline)
            .fontWeight(.bold)
            .fontDesign(.rounded)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.animalButton.gradient, in: .capsule)
            .shadow(color: Color.animalButton.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .padding()
        .background(Color.animalCardBackground.opacity(0.8), in: .rect(cornerRadius: 16))
        .transition(.scale.combined(with: .opacity))
    }

    private var noWordStateView: some View {
        VStack(spacing: 20) {
            if !UserDefaults.standard.bool(forKey: "wordImportSuccess") {
                // 词库导入中
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.5)
                        .tint(Color.animalAccent)
                    
                    Text("词库初始化中...")
                        .font(.headline)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalTextColor)
                    
                    Text("首次启动需要加载词库数据")
                        .font(.caption)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalSecondaryText)
                }
                .padding()
                .background(.ultraThinMaterial, in: .rect(cornerRadius: 16))
            } else {
                // 词库导入完成但没有单词
                VStack(spacing: 16) {
                    if viewModel.selectedCategory == .starred {
                        // 标星分类但没有标星单词的特殊显示
                        Image(systemName: "star.slash")
                            .font(.system(size: 40))
                            .foregroundColor(Color.animalSecondaryText)
                        
                        Text("还没有标星单词")
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalTextColor)
                        
                        Text("先去学习其他分类的单词，点击星星收藏吧！")
                            .font(.body)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalSecondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("选择其他分类") {
                            showCategorySelection = true
                        }
                        .font(.body)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.animalSecondaryText, in: .rect(cornerRadius: 8))
                        .foregroundColor(.white)
                    } else if viewModel.selectedCategory == .wrongWords {
                        // 错词本分类但没有错词的特殊显示
                        Image(systemName: "checkmark.circle")
                            .font(.system(size: 40))
                            .foregroundColor(Color.animalSuccess)
                        
                        Text("暂无错词")
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalTextColor)
                        
                        Text("继续学习单词，答错的单词会出现在这里")
                            .font(.body)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalSecondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("选择其他分类") {
                            showCategorySelection = true
                        }
                        .font(.body)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.animalSuccess, in: .rect(cornerRadius: 8))
                        .foregroundColor(.white)
                    } else if viewModel.selectedCategory == .currentArticle {
                        // 当前文章分类但没有文章的特殊显示
                        Image(systemName: "doc.text.slash")
                            .font(.system(size: 40))
                            .foregroundColor(Color.animalSuccess)
                        
                        Text("尚未选择文章")
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalTextColor)
                        
                        Text("请先添加文章并选择开始拼写")
                            .font(.body)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalSecondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("选择其他分类") {
                            showCategorySelection = true
                        }
                        .font(.body)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.animalSuccess, in: .rect(cornerRadius: 8))
                        .foregroundColor(.white)
                    } else {
                        // 其他分类没有单词
                        Image(systemName: "questionmark.circle")
                            .font(.system(size: 40))
                            .foregroundColor(Color.animalSecondaryText)
                        
                        Text("未找到单词")
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalTextColor)
                        
                        Button("重新加载") {
                            viewModel.forceRefresh()
                        }
                        .font(.body)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.animalAccent, in: .rect(cornerRadius: 8))
                        .foregroundColor(.white)
                    }
                }
                .padding()
                .background(.ultraThinMaterial, in: .rect(cornerRadius: 16))
            }
        }
    }
} 

import Foundation
import SwiftData

// MARK: - 游戏状态管理器
@Observable
final class WordGameManager: @unchecked Sendable {
    
    // MARK: - 游戏状态
    enum GameState {
        case playing
        case correct
        case outOfEnergy
    }
    
    private(set) var gameState: GameState = .playing
    private(set) var userInput: String = ""
    private(set) var hasErrorInCurrentWord: Bool = false
    private(set) var errorMessage: String = ""
    private(set) var showError: Bool = false
    
    // 依赖的管理器
    private let energyManager = EnergyManager.shared
    private let hapticManager = HapticManager.shared
    private let speechService = SpeechService.shared
    
    // MARK: - 游戏逻辑
    
    @MainActor
    func startNewGame() {
        gameState = .playing
        userInput = ""
        hasErrorInCurrentWord = false
        errorMessage = ""
        showError = false
    }
    
    @MainActor
    func processCorrectInput(_ text: String) {
        hapticManager.trigger(.letterSuccess)
        userInput += text
        clearError()
    }
    
    @MainActor
    func processIncorrectInput(expectedWord: String) {
        hapticManager.trigger(.letterFailure)
        hasErrorInCurrentWord = true
        
        let success = energyManager.consumeEnergy()
        if !success {
            gameState = .outOfEnergy
        }
    }
    
    @MainActor
    func completeWord() {
        gameState = .correct
        hapticManager.trigger(.wordComplete)
    }
    
    @MainActor
    func autoCompleteWord(_ fullWord: String) {
        userInput = fullWord
        gameState = .correct
    }
    
    @MainActor
    func showError(message: String) {
        errorMessage = message
        showError = true
    }
    
    @MainActor
    func clearError() {
        showError = false
        errorMessage = ""
    }
    
    @MainActor
    func retryAfterEnergyCheck() -> Bool {
        let wasReset = energyManager.forceCheckReset()
        if wasReset || energyManager.hasEnergy() {
            if gameState == .outOfEnergy {
                gameState = .playing
            }
            return true
        }
        return false
    }
    
    @MainActor
    func hasEnergy() -> Bool {
        return energyManager.hasEnergy()
    }
    
    @MainActor
    func setOutOfEnergy() {
        gameState = .outOfEnergy
    }
} 
